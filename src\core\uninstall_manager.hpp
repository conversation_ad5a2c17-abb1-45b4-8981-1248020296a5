#pragma once

#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <chrono>
#include <sstream>
#include <algorithm>
#include <iostream>
#include <spdlog/spdlog.h>
#include <nlohmann/json.hpp>
#include "config.hpp"
#include "manifest.hpp"
#include "dependency_resolver.hpp"
#include "../utils/shim_manager.hpp"

namespace sco {

class UninstallManager {
public:
    struct UninstallOptions {
        bool global = false;
        bool purge = false;  // Remove all data including persist directory
        bool force = false;  // Force uninstall even if other apps depend on it
    };
    
    struct UninstallResult {
        bool success = false;
        std::string error_message;
        std::vector<std::string> uninstalled_apps;
        std::vector<std::string> failed_apps;
        std::vector<std::string> skipped_apps;  // Apps that have dependents
        std::chrono::milliseconds total_duration{0};
    };
    
    static UninstallResult uninstall_apps(const std::vector<std::string>& app_names, 
                                         const UninstallOptions& options = {}) {
        UninstallManager manager(options);
        return manager.perform_uninstallation(app_names);
    }
    
    // Check what apps depend on the given apps
    static std::vector<std::string> get_dependents(const std::vector<std::string>& app_names) {
        std::vector<std::string> dependents;
        auto installed_apps = get_installed_apps();
        
        for (const auto& installed_app : installed_apps) {
            auto manifest = get_installed_manifest(installed_app);
            if (!manifest.is_valid()) continue;
            
            for (const auto& dep : manifest.depends) {
                std::string dep_name = extract_app_name(dep);
                if (std::find(app_names.begin(), app_names.end(), dep_name) != app_names.end()) {
                    if (std::find(dependents.begin(), dependents.end(), installed_app) == dependents.end()) {
                        dependents.push_back(installed_app);
                    }
                }
            }
        }
        
        return dependents;
    }
    
private:
    UninstallOptions options_;
    Config& config_;
    
    explicit UninstallManager(const UninstallOptions& options) 
        : options_(options), config_(Config::instance()) {
        config_.load();
        if (options_.global) {
            config_.set_global_mode(true);
        }
    }
    
    UninstallResult perform_uninstallation(const std::vector<std::string>& app_names) {
        UninstallResult result;
        auto start_time = std::chrono::steady_clock::now();
        
        std::cout << "Starting uninstallation of " << app_names.size() << " app(s)...\n";
        
        try {
            // Step 1: Validate that all apps are installed
            std::vector<std::string> valid_apps;
            for (const auto& app_name : app_names) {
                if (is_app_installed(app_name)) {
                    valid_apps.push_back(app_name);
                } else {
                    SPDLOG_WARN("App {} is not installed, skipping", app_name);
                    result.skipped_apps.push_back(app_name);
                }
            }
            
            if (valid_apps.empty()) {
                std::cout << "No valid apps to uninstall.\n";
                result.success = true;
                return result;
            }
            
            // Step 2: Check for dependents (unless force)
            if (!options_.force) {
                auto dependents = get_dependents(valid_apps);
                if (!dependents.empty()) {
                    result.error_message = "Cannot uninstall apps that other apps depend on. Use --force to override.";
                    result.skipped_apps.insert(result.skipped_apps.end(), dependents.begin(), dependents.end());
                    
                    SPDLOG_ERROR("Apps have dependents: {}", join_strings(dependents, ", "));
                    return result;
                }
            }
            
            // Step 3: Determine uninstall order (reverse dependency order)
            std::vector<std::string> uninstall_order = determine_uninstall_order(valid_apps);
            
            std::cout << "Uninstalling " << uninstall_order.size() << " app(s) in order: "
                      << join_strings(uninstall_order, ", ") << "\n";
            
            // Step 4: Uninstall each app in order
            for (const auto& app_name : uninstall_order) {
                if (uninstall_single_app(app_name)) {
                    result.uninstalled_apps.push_back(app_name);
                    std::cout << "✓ Successfully uninstalled: " << app_name << "\n";
                } else {
                    result.failed_apps.push_back(app_name);
                    std::cout << "✗ Failed to uninstall: " << app_name << "\n";
                }
            }
            
            result.success = result.failed_apps.empty();
            
        } catch (const std::exception& e) {
            result.error_message = e.what();
            SPDLOG_ERROR("Uninstallation failed with exception: {}", e.what());
        }
        
        auto end_time = std::chrono::steady_clock::now();
        result.total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        SPDLOG_DEBUG("Uninstallation completed in {}ms. Success: {}, Uninstalled: {}, Failed: {}",
                    result.total_duration.count(), result.success,
                    result.uninstalled_apps.size(), result.failed_apps.size());
        
        return result;
    }
    
    bool uninstall_single_app(const std::string& app_name) {
        std::cout << "Uninstalling: " << app_name << "...\n";
        
        try {
            // Step 1: Get app information
            auto app_dir = get_app_directory(app_name);
            if (!std::filesystem::exists(app_dir)) {
                SPDLOG_ERROR("App directory not found: {}", app_dir.string());
                return false;
            }
            
            auto manifest = get_installed_manifest(app_name);
            if (!manifest.is_valid()) {
                SPDLOG_WARN("Could not load manifest for {}, proceeding with basic uninstall", app_name);
            }
            
            SPDLOG_DEBUG("Uninstalling: {} from {}", app_name, app_dir.string());
            
            // Step 2: Run pre-uninstall script
            if (manifest.is_valid()) {
                auto pre_uninstall_script = manifest.get_pre_uninstall();
                if (!pre_uninstall_script.empty()) {
                    if (!run_script(pre_uninstall_script, app_dir, "pre-uninstall")) {
                        SPDLOG_WARN("Pre-uninstall script failed for: {}", app_name);
                        // Don't fail uninstall for script failures
                    }
                }
            }
            
            // Step 3: Remove shims
            if (!remove_app_shims(app_name, manifest)) {
                std::cout << "⚠ Failed to remove some shims for: " << app_name << "\n";
                // Don't fail uninstall for shim removal failures
            }
            
            // Step 4: Run post-uninstall script
            if (manifest.is_valid()) {
                auto post_uninstall_script = manifest.get_post_uninstall();
                if (!post_uninstall_script.empty()) {
                    if (!run_script(post_uninstall_script, app_dir, "post-uninstall")) {
                        SPDLOG_WARN("Post-uninstall script failed for: {}", app_name);
                        // Don't fail uninstall for script failures
                    }
                }
            }
            
            // Step 5: Remove registry entries (if any)
            if (!remove_registry_entries(app_name, manifest)) {
                std::cout << "⚠ Failed to remove some registry entries for: " << app_name << "\n";
                // Don't fail uninstall for registry cleanup failures
            }
            
            // Step 6: Remove app directory
            if (!remove_app_directory(app_name)) {
                std::cout << "✗ Failed to remove app directory for: " << app_name << "\n";
                return false;
            }
            
            return true;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Exception during uninstallation of {}: {}", app_name, e.what());
            return false;
        }
    }
    
    std::vector<std::string> determine_uninstall_order(const std::vector<std::string>& app_names) {
        // For uninstall, we want to remove apps in reverse dependency order
        // Apps that depend on others should be removed first
        
        std::vector<std::string> order;
        std::vector<std::string> remaining = app_names;
        
        while (!remaining.empty()) {
            bool found_removable = false;
            
            for (auto it = remaining.begin(); it != remaining.end(); ++it) {
                const auto& app_name = *it;
                
                // Check if any remaining app depends on this one
                bool has_dependent_in_remaining = false;
                for (const auto& other_app : remaining) {
                    if (other_app == app_name) continue;
                    
                    auto manifest = get_installed_manifest(other_app);
                    if (!manifest.is_valid()) continue;
                    
                    for (const auto& dep : manifest.depends) {
                        std::string dep_name = extract_app_name(dep);
                        if (dep_name == app_name) {
                            has_dependent_in_remaining = true;
                            break;
                        }
                    }
                    if (has_dependent_in_remaining) break;
                }
                
                if (!has_dependent_in_remaining) {
                    order.push_back(app_name);
                    remaining.erase(it);
                    found_removable = true;
                    break;
                }
            }
            
            if (!found_removable) {
                // Circular dependency or other issue, just add remaining apps
                order.insert(order.end(), remaining.begin(), remaining.end());
                break;
            }
        }
        
        return order;
    }
    
    bool remove_app_shims(const std::string& app_name, const Manifest& manifest) {
        bool success = true;
        
        if (manifest.is_valid()) {
            // Remove shims based on manifest bin entries
            auto bin_entries = manifest.get_bin();
            for (const auto& bin_entry : bin_entries) {
                std::string shim_name = extract_shim_name(bin_entry);
                if (!ShimManager::remove_shim(shim_name)) {
                    std::cout << "⚠ Failed to remove shim: " << shim_name << "\n";
                    success = false;
                }
            }
        } else {
            // Fallback: remove all shims that point to this app
            if (!ShimManager::remove_shims_for_app(app_name)) {
                std::cout << "⚠ Failed to remove shims for app: " << app_name << "\n";
                success = false;
            }
        }
        
        return success;
    }
    
    bool remove_registry_entries(const std::string& app_name, const Manifest& manifest) {
        // TODO: Implement registry cleanup
        // This would involve:
        // 1. Reading any registry entries created during installation
        // 2. Removing them safely
        // 3. Handling Windows-specific registry operations
        
        SPDLOG_DEBUG("Registry cleanup not yet implemented for: {}", app_name);
        return true;
    }
    
    bool remove_app_directory(const std::string& app_name) {
        try {
            auto app_dir = get_app_directory(app_name);
            
            if (!std::filesystem::exists(app_dir)) {
                SPDLOG_DEBUG("App directory already removed: {}", app_dir.string());
                return true;
            }
            
            // Remove the entire app directory
            std::filesystem::remove_all(app_dir);

            std::cout << "  Removed app directory: " << app_dir.string() << "\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "✗ Failed to remove app directory for " << app_name << ": " << e.what() << "\n";
            return false;
        }
    }
    
    bool run_script(const std::string& script, 
                   const std::filesystem::path& app_dir,
                   const std::string& script_type) {
        SPDLOG_INFO("Running {} script", script_type);
        
        try {
            // Create temporary script file
            auto temp_script = app_dir / ("temp_" + script_type + ".ps1");
            
            std::ofstream script_file(temp_script);
            if (!script_file.is_open()) {
                SPDLOG_ERROR("Failed to create temporary script file");
                return false;
            }
            
            script_file << script;
            script_file.close();
            
            // Execute script
            std::string command = "powershell.exe -ExecutionPolicy Bypass -File \"" + 
                                temp_script.string() + "\"";
            
            SPDLOG_DEBUG("Executing script: {}", command);
            
            int exit_code = system(command.c_str());
            
            // Clean up temporary script
            std::filesystem::remove(temp_script);
            
            if (exit_code == 0) {
                SPDLOG_INFO("{} script completed successfully", script_type);
                return true;
            } else {
                SPDLOG_ERROR("{} script failed with exit code: {}", script_type, exit_code);
                return false;
            }
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to run {} script: {}", script_type, e.what());
            return false;
        }
    }
    
public:
    // Helper methods
    static bool is_app_installed(const std::string& app_name) {
        return DependencyResolver::is_app_installed(app_name);
    }

    static std::vector<std::string> get_installed_apps() {
        std::vector<std::string> apps;
        
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        
        if (!std::filesystem::exists(apps_dir)) {
            return apps;
        }
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
                if (entry.is_directory()) {
                    std::string app_name = entry.path().filename().string();
                    
                    // Check if there's a "current" directory or version directories
                    auto current_dir = entry.path() / "current";
                    if (std::filesystem::exists(current_dir)) {
                        apps.push_back(app_name);
                    } else {
                        // Check for version directories
                        bool has_version = false;
                        for (const auto& version_entry : std::filesystem::directory_iterator(entry.path())) {
                            if (version_entry.is_directory()) {
                                has_version = true;
                                break;
                            }
                        }
                        if (has_version) {
                            apps.push_back(app_name);
                        }
                    }
                }
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to get installed apps: {}", e.what());
        }
        
        return apps;
    }

private:
    static Manifest get_installed_manifest(const std::string& app_name) {
        auto& config = Config::instance();

        // Check local installation first
        auto local_app_dir = config.get_apps_dir() / app_name;
        auto info_file = local_app_dir / "current" / "scoop-install.json";
        if (!std::filesystem::exists(info_file)) {
            info_file = local_app_dir / "scoop-install.json";
        }

        // If local installation info not found, check global
        if (!std::filesystem::exists(info_file)) {
            auto global_app_dir = config.get_global_apps_dir() / app_name;
            info_file = global_app_dir / "current" / "scoop-install.json";
            if (!std::filesystem::exists(info_file)) {
                info_file = global_app_dir / "scoop-install.json";
            }
        }

        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;

                if (json.contains("bucket")) {
                    std::string bucket = json["bucket"].get<std::string>();
                    return ManifestParser::find_and_parse(app_name, bucket);
                }
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Failed to read installation info for {}: {}", app_name, e.what());
            }
        }

        // Fallback: search in all buckets
        return ManifestParser::find_and_parse(app_name);
    }
    
    std::filesystem::path get_app_directory(const std::string& app_name) {
        // Check local installation first
        auto local_app_dir = config_.get_apps_dir() / app_name;
        if (std::filesystem::exists(local_app_dir)) {
            return local_app_dir;
        }

        // Check global installation if in global mode or if local doesn't exist
        if (options_.global) {
            auto global_app_dir = config_.get_global_apps_dir() / app_name;
            if (std::filesystem::exists(global_app_dir)) {
                return global_app_dir;
            }
        }

        // If not in global mode but local doesn't exist, still check global as fallback
        if (!options_.global) {
            auto global_app_dir = config_.get_global_apps_dir() / app_name;
            if (std::filesystem::exists(global_app_dir)) {
                return global_app_dir;
            }
        }

        // Return local path as default (even if it doesn't exist)
        return local_app_dir;
    }
    
    static std::string extract_app_name(const std::string& dependency) {
        // Handle different dependency formats:
        // - "app_name"
        // - "bucket/app_name"
        // - "app_name@version"
        // - "bucket/app_name@version"
        
        std::string result = dependency;
        
        // Remove version specifier if present
        size_t at_pos = result.find('@');
        if (at_pos != std::string::npos) {
            result = result.substr(0, at_pos);
        }
        
        // Extract app name from bucket/app format
        size_t slash_pos = result.find('/');
        if (slash_pos != std::string::npos) {
            result = result.substr(slash_pos + 1);
        }
        
        return result;
    }
    
    static std::string extract_shim_name(const std::string& bin_entry) {
        // Extract shim name from bin entry
        // For now, assume simple string format
        // TODO: Parse JSON array format properly
        return std::filesystem::path(bin_entry).stem().string();
    }
    
    template<typename Container>
    std::string join_strings(const Container& container, const std::string& delimiter) {
        if (container.empty()) return "";

        std::ostringstream oss;
        auto it = container.begin();
        oss << *it;
        ++it;

        for (; it != container.end(); ++it) {
            oss << delimiter << *it;
        }

        return oss.str();
    }
};

} // namespace sco
