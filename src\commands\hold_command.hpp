#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <vector>
#include <string>
#include <set>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

namespace sco {

class HoldCommand : public BaseCommand {
public:
    HoldCommand() = default;
    
    int execute() override {
        try {
            if (app_names_.empty()) {
                // Show currently held apps
                return show_held_apps();
            } else {
                // Hold specified apps
                return hold_apps();
            }
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Hold command failed: {}", e.what());
            std::cerr << "Hold command failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "hold"; }
    std::string get_description() const override { return "Hold an app to disable updates"; }
    
    void set_app_names(const std::vector<std::string>& app_names) { app_names_ = app_names; }
    void set_global(bool global) { global_ = global; }
    
private:
    std::vector<std::string> app_names_;
    bool global_ = false;
    
    int show_held_apps() {
        auto held_apps = get_held_apps();
        
        if (held_apps.empty()) {
            std::cout << "No apps are currently held.\n";
            return 0;
        }
        
        std::cout << "Held apps:\n\n";
        
        TableFormatter table;
        table.add_column("Name", true);
        table.add_column("Version", true);
        table.add_column("Scope", true);
        
        for (const auto& app : held_apps) {
            table.add_row({
                app.name,
                app.version,
                app.global ? "Global" : "Local"
            });
        }
        
        table.print();
        
        std::cout << "\nTotal: " << held_apps.size() << " held app(s)\n";
        std::cout << "Use 'sco unhold <app>' to enable updates for an app.\n";
        
        return 0;
    }
    
    int hold_apps() {
        std::cout << "Holding apps: ";
        for (size_t i = 0; i < app_names_.size(); ++i) {
            std::cout << app_names_[i];
            if (i < app_names_.size() - 1) std::cout << ", ";
        }
        std::cout << "\n\n";
        
        bool all_success = true;
        std::vector<std::string> held_apps;
        std::vector<std::string> failed_apps;
        std::vector<std::string> not_installed;
        
        for (const auto& app_name : app_names_) {
            if (!is_app_installed(app_name)) {
                not_installed.push_back(app_name);
                continue;
            }
            
            if (hold_app(app_name)) {
                held_apps.push_back(app_name);
            } else {
                failed_apps.push_back(app_name);
                all_success = false;
            }
        }
        
        // Show results
        if (!not_installed.empty()) {
            std::cout << "The following apps are not installed:\n";
            for (const auto& app : not_installed) {
                std::cout << "  - " << app << "\n";
            }
            std::cout << "\n";
        }
        
        if (!held_apps.empty()) {
            std::cout << "Successfully held:\n";
            for (const auto& app : held_apps) {
                std::cout << "  + " << app << "\n";
            }
        }
        
        if (!failed_apps.empty()) {
            std::cout << "Failed to hold:\n";
            for (const auto& app : failed_apps) {
                std::cout << "  ✗ " << app << "\n";
            }
        }
        
        return all_success ? 0 : 1;
    }
    
    struct HeldApp {
        std::string name;
        std::string version;
        bool global;
    };
    
    std::vector<HeldApp> get_held_apps() {
        std::vector<HeldApp> held_apps;
        
        auto held_set = load_held_apps();
        
        for (const auto& app_name : held_set) {
            HeldApp app;
            app.name = app_name;
            app.version = get_app_version(app_name);
            app.global = is_app_global(app_name);
            held_apps.push_back(app);
        }
        
        return held_apps;
    }
    
    bool is_app_installed(const std::string& app_name) {
        auto& config = Config::instance();
        
        // Check local installation
        auto local_app_dir = config.get_apps_dir() / app_name;
        if (std::filesystem::exists(local_app_dir)) {
            return true;
        }
        
        // Check global installation
        auto global_app_dir = config.get_global_apps_dir() / app_name;
        if (std::filesystem::exists(global_app_dir)) {
            return true;
        }
        
        return false;
    }
    
    bool is_app_global(const std::string& app_name) {
        auto& config = Config::instance();
        auto global_app_dir = config.get_global_apps_dir() / app_name;
        return std::filesystem::exists(global_app_dir);
    }
    
    std::string get_app_version(const std::string& app_name) {
        auto& config = Config::instance();
        
        // Try local first
        auto app_dir = config.get_apps_dir() / app_name;
        if (!std::filesystem::exists(app_dir)) {
            // Try global
            app_dir = config.get_global_apps_dir() / app_name;
        }
        
        if (!std::filesystem::exists(app_dir)) {
            return "unknown";
        }
        
        // Try to read from installation info
        auto info_file = app_dir / "current" / "scoop-install.json";
        if (!std::filesystem::exists(info_file)) {
            info_file = app_dir / "scoop-install.json";
        }
        
        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;
                
                if (json.contains("version")) {
                    return json["version"].get<std::string>();
                }
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Failed to read installation info for {}: {}", app_name, e.what());
            }
        }
        
        return "unknown";
    }
    
    bool hold_app(const std::string& app_name) {
        auto held_set = load_held_apps();
        
        if (held_set.find(app_name) != held_set.end()) {
            std::cout << "  " << app_name << " is already held.\n";
            return true;
        }
        
        held_set.insert(app_name);
        
        if (save_held_apps(held_set)) {
            std::cout << "  " << app_name << " is now held.\n";
            return true;
        } else {
            std::cout << "  Failed to hold " << app_name << ".\n";
            return false;
        }
    }
    
    std::set<std::string> load_held_apps() {
        std::set<std::string> held_apps;
        
        auto& config = Config::instance();
        auto held_file = config.get_scoop_dir() / "held.json";
        
        if (!std::filesystem::exists(held_file)) {
            return held_apps;
        }
        
        try {
            std::ifstream file(held_file);
            nlohmann::json json;
            file >> json;
            
            if (json.is_array()) {
                for (const auto& app : json) {
                    if (app.is_string()) {
                        held_apps.insert(app.get<std::string>());
                    }
                }
            }
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to load held apps: {}", e.what());
        }
        
        return held_apps;
    }
    
    bool save_held_apps(const std::set<std::string>& held_apps) {
        auto& config = Config::instance();
        auto held_file = config.get_scoop_dir() / "held.json";
        
        try {
            // Create directory if it doesn't exist
            std::filesystem::create_directories(held_file.parent_path());
            
            nlohmann::json json = nlohmann::json::array();
            for (const auto& app : held_apps) {
                json.push_back(app);
            }
            
            std::ofstream file(held_file);
            if (!file.is_open()) {
                return false;
            }
            
            file << json.dump(2);
            file.close();
            
            return true;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to save held apps: {}", e.what());
            return false;
        }
    }
};

class UnholdCommand : public BaseCommand {
public:
    UnholdCommand() = default;
    
    int execute() override {
        try {
            if (app_names_.empty()) {
                std::cerr << "App name(s) required.\n";
                std::cout << "Usage: sco unhold <app1> [app2] ...\n";
                return 1;
            }

            return unhold_apps();
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Unhold command failed: {}", e.what());
            std::cerr << "Unhold command failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "unhold"; }
    std::string get_description() const override { return "Unhold an app to enable updates"; }
    
    void set_app_names(const std::vector<std::string>& app_names) { app_names_ = app_names; }
    
private:
    std::vector<std::string> app_names_;
    
    int unhold_apps() {
        std::cout << "Unholding apps: ";
        for (size_t i = 0; i < app_names_.size(); ++i) {
            std::cout << app_names_[i];
            if (i < app_names_.size() - 1) std::cout << ", ";
        }
        std::cout << "\n\n";
        
        auto held_set = load_held_apps();
        bool any_changes = false;
        
        for (const auto& app_name : app_names_) {
            if (held_set.find(app_name) != held_set.end()) {
                held_set.erase(app_name);
                std::cout << "  + " << app_name << " is no longer held.\n";
                any_changes = true;
            } else {
                std::cout << "  - " << app_name << " was not held.\n";
            }
        }
        
        if (any_changes) {
            if (save_held_apps(held_set)) {
                std::cout << "\nChanges saved successfully.\n";
                return 0;
            } else {
                std::cout << "\nFailed to save changes.\n";
                return 1;
            }
        } else {
            std::cout << "\nNo changes made.\n";
            return 0;
        }
    }
    
    std::set<std::string> load_held_apps() {
        std::set<std::string> held_apps;
        
        auto& config = Config::instance();
        auto held_file = config.get_scoop_dir() / "held.json";
        
        if (!std::filesystem::exists(held_file)) {
            return held_apps;
        }
        
        try {
            std::ifstream file(held_file);
            nlohmann::json json;
            file >> json;
            
            if (json.is_array()) {
                for (const auto& app : json) {
                    if (app.is_string()) {
                        held_apps.insert(app.get<std::string>());
                    }
                }
            }
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to load held apps: {}", e.what());
        }
        
        return held_apps;
    }
    
    bool save_held_apps(const std::set<std::string>& held_apps) {
        auto& config = Config::instance();
        auto held_file = config.get_scoop_dir() / "held.json";
        
        try {
            nlohmann::json json = nlohmann::json::array();
            for (const auto& app : held_apps) {
                json.push_back(app);
            }
            
            std::ofstream file(held_file);
            if (!file.is_open()) {
                return false;
            }
            
            file << json.dump(2);
            file.close();
            
            return true;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to save held apps: {}", e.what());
            return false;
        }
    }
};

} // namespace sco
