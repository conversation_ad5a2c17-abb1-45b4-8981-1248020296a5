#pragma once

#include <iostream>
#include <string>

namespace sco {
namespace output {

// Global flags for controlling output
extern bool verbose;
extern bool quiet;

// Output functions
void info(const std::string& message);
void warn(const std::string& message);
void error(const std::string& message);
void debug(const std::string& message);  // Only shown in verbose mode
void success(const std::string& message);

// Note: For now we use simple string concatenation
// More advanced formatting can be added later if needed

// Initialize output system
void init(bool verbose_mode = false, bool quiet_mode = false);

} // namespace output
} // namespace sco
