#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <vector>
#include <algorithm>
#include <sstream>
#include <chrono>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

namespace sco {

struct AppInfo {
    std::string name;
    std::string version;
    std::string source;
    std::string updated;
    std::string info;
};

class ListCommand : public BaseCommand {
public:
    ListCommand() = default;
    
    int execute() override {
        try {
            list_installed_apps();
            return 0;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to list apps: {}", e.what());
            return 1;
        }
    }
    
    std::string get_name() const override { return "list"; }
    std::string get_description() const override { return "List installed apps"; }
    
private:
    void list_installed_apps() {
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        auto global_apps_dir = config.get_global_apps_dir();

        SPDLOG_DEBUG("Checking apps directory: {}", apps_dir.string());
        SPDLOG_DEBUG("Checking global apps directory: {}", global_apps_dir.string());

        std::vector<AppInfo> apps;

        // Check local apps
        if (std::filesystem::exists(apps_dir)) {
            try {
                for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
                    if (entry.is_directory()) {
                        std::string app_name = entry.path().filename().string();

                        // Skip scoop itself
                        if (app_name == "scoop") {
                            continue;
                        }

                        AppInfo app_info;
                        app_info.name = app_name;
                        app_info.version = get_app_version(entry.path());
                        app_info.source = get_app_source(entry.path());
                        app_info.updated = get_app_updated_time(entry.path());
                        app_info.info = get_app_info(entry.path());

                        apps.push_back(app_info);
                        SPDLOG_DEBUG("Found local app: {} version {} from {}", app_name, app_info.version, app_info.source);
                    }
                }
            } catch (const std::filesystem::filesystem_error& e) {
                SPDLOG_ERROR("Filesystem error in local apps: {}", e.what());
            }
        }

        // Check global apps
        if (std::filesystem::exists(global_apps_dir)) {
            try {
                for (const auto& entry : std::filesystem::directory_iterator(global_apps_dir)) {
                    if (entry.is_directory()) {
                        std::string app_name = entry.path().filename().string();

                        // Skip scoop itself
                        if (app_name == "scoop") {
                            continue;
                        }

                        // Check if we already have this app from local installation
                        bool already_exists = false;
                        for (const auto& existing_app : apps) {
                            if (existing_app.name == app_name) {
                                already_exists = true;
                                break;
                            }
                        }

                        if (!already_exists) {
                            AppInfo app_info;
                            app_info.name = app_name + " (global)";
                            app_info.version = get_app_version(entry.path());
                            app_info.source = get_app_source(entry.path());
                            app_info.updated = get_app_updated_time(entry.path());
                            app_info.info = get_app_info(entry.path());

                            apps.push_back(app_info);
                            SPDLOG_DEBUG("Found global app: {} version {} from {}", app_name, app_info.version, app_info.source);
                        }
                    }
                }
            } catch (const std::filesystem::filesystem_error& e) {
                SPDLOG_ERROR("Filesystem error in global apps: {}", e.what());
            }
        }

        if (apps.empty()) {
            std::cout << "No apps installed.\n";
            return;
        }

        if (apps.empty()) {
            std::cout << "No apps installed.\n";
            return;
        }

        // Sort apps by name
        std::sort(apps.begin(), apps.end(), [](const AppInfo& a, const AppInfo& b) {
            return a.name < b.name;
        });

        print_apps_table(apps);
    }

private:
    std::string get_app_version(const std::filesystem::path& app_path) {
        // Look for current.txt first
        auto current_file = app_path / "current.txt";
        if (std::filesystem::exists(current_file)) {
            std::ifstream file(current_file);
            if (file.is_open()) {
                std::string version;
                std::getline(file, version);
                return version.empty() ? "unknown" : version;
            }
        }

        // Check for version directories
        try {
            for (const auto& version_entry : std::filesystem::directory_iterator(app_path)) {
                if (version_entry.is_directory()) {
                    std::string dir_name = version_entry.path().filename().string();
                    // Skip special directories
                    if (dir_name != "current" && dir_name != "persist") {
                        return dir_name;
                    }
                }
            }
        } catch (const std::exception&) {
            // Ignore errors
        }

        return "unknown";
    }

    std::string get_app_source(const std::filesystem::path& app_path) {
        // Try to read from install.json or manifest
        auto install_json = app_path / "current" / "install.json";
        if (std::filesystem::exists(install_json)) {
            try {
                std::ifstream file(install_json);
                if (file.is_open()) {
                    nlohmann::json install_info;
                    file >> install_info;
                    if (install_info.contains("bucket")) {
                        return install_info["bucket"].get<std::string>();
                    }
                }
            } catch (const std::exception&) {
                // Ignore JSON parsing errors
            }
        }

        // Try to read from scoop-install.json (alternative location)
        auto scoop_install = app_path / "current" / "scoop-install.json";
        if (std::filesystem::exists(scoop_install)) {
            try {
                std::ifstream file(scoop_install);
                if (file.is_open()) {
                    nlohmann::json install_info;
                    file >> install_info;
                    if (install_info.contains("bucket")) {
                        return install_info["bucket"].get<std::string>();
                    }
                }
            } catch (const std::exception&) {
                // Ignore JSON parsing errors
            }
        }

        return "unknown";
    }

    std::string get_app_updated_time(const std::filesystem::path& app_path) {
        try {
            // Get the last write time of the current directory or current.txt
            auto current_dir = app_path / "current";
            auto current_file = app_path / "current.txt";

            std::filesystem::file_time_type last_time;
            bool found_time = false;

            if (std::filesystem::exists(current_dir)) {
                last_time = std::filesystem::last_write_time(current_dir);
                found_time = true;
            } else if (std::filesystem::exists(current_file)) {
                last_time = std::filesystem::last_write_time(current_file);
                found_time = true;
            }

            if (found_time) {
                // Convert to system time
                auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                    last_time - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
                auto time_t = std::chrono::system_clock::to_time_t(sctp);

                std::stringstream ss;
                ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
                return ss.str();
            }
        } catch (const std::exception&) {
            // Ignore errors
        }

        return "";
    }

    std::string get_app_info(const std::filesystem::path& app_path) {
        // For now, return empty string
        // In the future, this could contain additional info like "global", "held", etc.
        (void)app_path; // Suppress unused parameter warning
        return "";
    }

    void print_apps_table(const std::vector<AppInfo>& apps) {
        std::cout << "Installed apps:\n\n";

        // Create table formatter
        TableFormatter table;

        // Define columns (width auto-calculated)
        table.add_column("Name", true);      // Left-aligned
        table.add_column("Version", true);   // Left-aligned
        table.add_column("Source", true);    // Left-aligned
        table.add_column("Updated", true);   // Left-aligned
        table.add_column("Info", true);      // Left-aligned

        // Add data rows
        for (const auto& app : apps) {
            table.add_row({
                app.name,
                app.version,
                app.source,
                app.updated,
                app.info
            });
        }

        // Print the table (auto-adjusts widths)
        table.print();
    }
};

} // namespace sco
