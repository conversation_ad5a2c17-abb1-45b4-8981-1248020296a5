#include "output.hpp"
#include <sstream>
#include <iomanip>

namespace sco {
namespace output {

// Global flags
bool verbose = false;
bool quiet = false;

void init(bool verbose_mode, bool quiet_mode) {
    verbose = verbose_mode;
    quiet = quiet_mode;
}

void info(const std::string& message) {
    if (!quiet) {
        std::cout << message << std::endl;
    }
}

void warn(const std::string& message) {
    if (!quiet) {
        std::cout << "! " << message << std::endl;
    }
}

void error(const std::string& message) {
    std::cerr << "- " << message << std::endl;
}

void debug(const std::string& message) {
    if (verbose && !quiet) {
        std::cout << "[DEBUG] " << message << std::endl;
    }
}

void success(const std::string& message) {
    if (!quiet) {
        std::cout << "+ " << message << std::endl;
    }
}

// Simple string formatting - for now we'll keep it basic
// More advanced formatting can be added later if needed

} // namespace output
} // namespace sco
