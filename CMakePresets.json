﻿{
    "version": 8,
    "configurePresets": [
        {
            "name": "msvc-debug",
            "generator": "Ninja",
            "binaryDir": "Build/${presetName}",
            "cacheVariables": {
                "CMAKE_BUILD_TYPE": "Debug",
                "CMAKE_C_COMPILER": "cl",
                "CMAKE_CXX_COMPILER": "cl",
                "CMAKE_TOOLCHAIN_FILE": "d:/Local/vcpkg-latest/scripts/buildsystems/vcpkg.cmake"
            }
        },
        {
            "name": "msvc-release",
            "inherits": "msvc-debug",
            "binaryDir": "Build/${presetName}",
            "cacheVariables": {
                "CMAKE_BUILD_TYPE": "Release"
            }
        },
        {
            "name": "gcc-release",
            "generator": "Ninja",
            "binaryDir": "Build/${presetName}",
            "cacheVariables": {
                "CMAKE_BUILD_TYPE": "Release"
            }
        },
        {
            "name": "gcc-debug",
            "inherits": "gcc-release",
            "binaryDir": "Build/${presetName}",
            "cacheVariables": {
                "CMAKE_BUILD_TYPE": "Debug"
            }
        }
    ]
}