
cmake_minimum_required(VERSION 3.15)

project(sco VERSION 0.1.0 LANGUAGES CXX)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 查找依赖包
find_package(CLI11 CONFIG REQUIRED)
find_package(nlohmann_json CONFIG REQUIRED)
find_package(spdlog CONFIG REQUIRED)

# 添加源文件
file(GLOB_RECURSE SOURCES "src/*.cpp")

# 创建可执行文件
add_executable(sco ${SOURCES})

# 设置包含目录
target_include_directories(sco PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_BINARY_DIR}
)

# 链接依赖库
target_link_libraries(sco PRIVATE
    CLI11::CLI11
    nlohmann_json::nlohmann_json
    spdlog::spdlog_header_only
)

# 设置编译选项
if(MSVC)
    target_compile_options(sco PRIVATE /utf-8)
else()
    target_compile_options(sco PRIVATE -Wall -Wextra -Wpedantic)
endif()

# 安装规则
include(GNUInstallDirs)
install(TARGETS sco
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)