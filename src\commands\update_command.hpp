#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/update_manager.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <vector>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <algorithm>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

namespace sco {

class UpdateCommand : public BaseCommand {
public:
    UpdateCommand() = default;
    
    int execute() override {
        try {
            SPDLOG_DEBUG("Update command called");
            
            if (update_all_) {
                return update_all_apps();
            } else if (!apps_.empty()) {
                return update_specific_apps();
            } else {
                return update_scoop_itself();
            }
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Update command failed: {}", e.what());
            std::cerr << "Update failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "update"; }
    std::string get_description() const override { return "Update apps, or Scoop itself"; }
    
    // Setters for command options
    void set_apps(const std::vector<std::string>& apps) { apps_ = apps; }
    void set_update_all(bool update_all) { update_all_ = update_all; }
    void set_global(bool global) { global_ = global; }
    void set_force(bool force) { force_ = force; }
    void set_no_cache(bool no_cache) { no_cache_ = no_cache; }
    void set_skip_dependencies(bool skip) { skip_dependencies_ = skip; }
    
private:
    std::vector<std::string> apps_;
    bool update_all_ = false;
    bool global_ = false;
    bool force_ = false;
    bool no_cache_ = false;
    bool skip_dependencies_ = false;
    
    int update_all_apps() {
        std::cout << "Updating all installed apps...\n";
        
        // Get list of installed apps
        auto installed_apps = get_installed_apps();
        if (installed_apps.empty()) {
            std::cout << "No apps are currently installed.\n";
            return 0;
        }
        
        std::cout << "Found " << installed_apps.size() << " installed app(s).\n\n";
        
        // Check for updates
        auto updates_available = check_for_updates(installed_apps);
        if (updates_available.empty()) {
            std::cout << "All apps are up to date.\n";
            return 0;
        }
        
        // Show what will be updated
        show_update_summary(updates_available);
        
        // Perform updates
        UpdateManager::UpdateOptions options;
        options.global = global_;
        options.force = force_;
        options.no_cache = no_cache_;
        options.skip_dependencies = skip_dependencies_;
        
        auto start_time = std::chrono::steady_clock::now();
        auto result = UpdateManager::update_apps(updates_available, options);
        auto end_time = std::chrono::steady_clock::now();
        
        show_update_results(result, start_time, end_time);
        
        return result.success ? 0 : 1;
    }
    
    int update_specific_apps() {
        std::cout << "Updating specific apps: ";
        for (size_t i = 0; i < apps_.size(); ++i) {
            std::cout << apps_[i];
            if (i < apps_.size() - 1) std::cout << ", ";
        }
        std::cout << "\n\n";
        
        // Check which apps are installed
        auto installed_apps = get_installed_apps();
        std::vector<std::string> apps_to_update;
        std::vector<std::string> not_installed;
        
        for (const auto& app : apps_) {
            if (std::find(installed_apps.begin(), installed_apps.end(), app) != installed_apps.end()) {
                apps_to_update.push_back(app);
            } else {
                not_installed.push_back(app);
            }
        }
        
        // Report apps that are not installed
        if (!not_installed.empty()) {
            std::cout << "The following apps are not installed:\n";
            for (const auto& app : not_installed) {
                std::cout << "  - " << app << "\n";
            }
            std::cout << "\n";
        }
        
        if (apps_to_update.empty()) {
            std::cout << "No specified apps are currently installed.\n";
            return 1;
        }
        
        // Check for updates
        auto updates_available = check_for_updates(apps_to_update);
        if (updates_available.empty()) {
            std::cout << "All specified apps are up to date.\n";
            return 0;
        }
        
        // Show what will be updated
        show_update_summary(updates_available);
        
        // Perform updates
        UpdateManager::UpdateOptions options;
        options.global = global_;
        options.force = force_;
        options.no_cache = no_cache_;
        options.skip_dependencies = skip_dependencies_;
        
        auto start_time = std::chrono::steady_clock::now();
        auto result = UpdateManager::update_apps(updates_available, options);
        auto end_time = std::chrono::steady_clock::now();
        
        show_update_results(result, start_time, end_time);
        
        return result.success ? 0 : 1;
    }
    
    int update_scoop_itself() {
        std::cout << "Updating Scoop itself...\n";
        
        // Check if we're running the C++ version or PowerShell version
        std::cout << "Note: This is the C++ implementation of Scoop (sco).\n";
        std::cout << "To update sco, please use your package manager or download the latest release.\n";
        
        // Update buckets instead
        std::cout << "\nUpdating buckets...\n";
        auto result = update_buckets();
        
        if (result) {
            std::cout << "Buckets updated successfully.\n";
            return 0;
        } else {
            std::cout << "Failed to update some buckets.\n";
            return 1;
        }
    }
    
    std::vector<std::string> get_installed_apps() {
        std::vector<std::string> apps;
        
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        
        if (!std::filesystem::exists(apps_dir)) {
            return apps;
        }
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
                if (entry.is_directory()) {
                    std::string app_name = entry.path().filename().string();
                    // Skip special directories
                    if (app_name != "scoop" && app_name != ".git") {
                        apps.push_back(app_name);
                    }
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            SPDLOG_ERROR("Error reading apps directory: {}", e.what());
        }
        
        return apps;
    }
    
    std::vector<UpdateManager::UpdateInfo> check_for_updates(const std::vector<std::string>& apps) {
        std::vector<UpdateManager::UpdateInfo> updates;
        
        std::cout << "Checking for updates...\n";
        
        for (const auto& app : apps) {
            UpdateManager::UpdateInfo info;
            info.name = app;
            info.current_version = get_installed_version(app);

            // Get latest version from manifest
            auto manifest_info = get_latest_manifest_info(app);
            info.latest_version = manifest_info.first;
            info.bucket = manifest_info.second;

            if (!info.latest_version.empty() &&
                info.current_version != info.latest_version) {
                updates.push_back(info);
            }
        }
        
        return updates;
    }
    
    std::string get_installed_version(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            return "";
        }
        
        // Try to read from installation info
        auto info_file = app_dir / "current" / "scoop-install.json";
        if (!std::filesystem::exists(info_file)) {
            info_file = app_dir / "scoop-install.json";
        }
        
        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;
                
                if (json.contains("version")) {
                    return json["version"].get<std::string>();
                }
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Failed to read installation info for {}: {}", app_name, e.what());
            }
        }
        
        return "unknown";
    }
    
    std::pair<std::string, std::string> get_latest_manifest_info(const std::string& app_name) {
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        
        if (!std::filesystem::exists(buckets_dir)) {
            return {"", ""};
        }
        
        // Search through all buckets for the app manifest
        try {
            for (const auto& bucket_entry : std::filesystem::directory_iterator(buckets_dir)) {
                if (bucket_entry.is_directory()) {
                    std::string bucket_name = bucket_entry.path().filename().string();
                    auto manifest_path = bucket_entry.path() / "bucket" / (app_name + ".json");
                    
                    if (std::filesystem::exists(manifest_path)) {
                        try {
                            std::ifstream file(manifest_path);
                            nlohmann::json manifest;
                            file >> manifest;
                            
                            if (manifest.contains("version")) {
                                return {manifest["version"].get<std::string>(), bucket_name};
                            }
                        } catch (const std::exception& e) {
                            SPDLOG_DEBUG("Failed to read manifest for {}: {}", app_name, e.what());
                        }
                    }
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            SPDLOG_ERROR("Error searching for manifest: {}", e.what());
        }
        
        return {"", ""};
    }
    
    void show_update_summary(const std::vector<UpdateManager::UpdateInfo>& updates) {
        std::cout << "The following apps will be updated:\n\n";
        
        TableFormatter table;
        table.add_column("Name", true);
        table.add_column("Current", true);
        table.add_column("Latest", true);
        table.add_column("Source", true);
        
        for (const auto& update : updates) {
            table.add_row({
                update.name,
                update.current_version,
                update.latest_version,
                update.bucket
            });
        }
        
        table.print();
        std::cout << "\n";
    }
    
    void show_update_results(const UpdateManager::UpdateResult& result,
                           const std::chrono::steady_clock::time_point& start_time,
                           const std::chrono::steady_clock::time_point& end_time) {
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);
        
        std::cout << "\nUpdate completed in " << duration.count() << " seconds.\n";
        
        if (!result.updated_apps.empty()) {
            std::cout << "\nSuccessfully updated:\n";
            for (const auto& app : result.updated_apps) {
                std::cout << "  ✓ " << app << "\n";
            }
        }
        
        if (!result.failed_apps.empty()) {
            std::cout << "\nFailed to update:\n";
            for (const auto& app : result.failed_apps) {
                std::cout << "  ✗ " << app << "\n";
            }
        }
        
        if (!result.skipped_apps.empty()) {
            std::cout << "\nSkipped (already up to date):\n";
            for (const auto& app : result.skipped_apps) {
                std::cout << "  - " << app << "\n";
            }
        }
    }
    
    bool update_buckets() {
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        
        if (!std::filesystem::exists(buckets_dir)) {
            std::cout << "No buckets directory found.\n";
            return true; // Not an error if no buckets exist
        }
        
        bool all_success = true;
        
        try {
            for (const auto& bucket_entry : std::filesystem::directory_iterator(buckets_dir)) {
                if (bucket_entry.is_directory()) {
                    std::string bucket_name = bucket_entry.path().filename().string();
                    std::cout << "Updating bucket: " << bucket_name << "... ";
                    
                    // Check if it's a git repository
                    auto git_dir = bucket_entry.path() / ".git";
                    if (std::filesystem::exists(git_dir)) {
                        // Try to update using git
                        std::string command = "git -C \"" + bucket_entry.path().string() + "\" pull";
                        int result = std::system(command.c_str());
                        
                        if (result == 0) {
                            std::cout << "OK\n";
                        } else {
                            std::cout << "Failed\n";
                            all_success = false;
                        }
                    } else {
                        std::cout << "Not a git repository, skipping\n";
                    }
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            SPDLOG_ERROR("Error updating buckets: {}", e.what());
            all_success = false;
        }
        
        return all_success;
    }
};

} // namespace sco
